// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "./interfaces/IStorageProof.sol";
import "../contracts/core/StorageToken.sol";
import "./interfaces/IRewardEngine.sol";
import "./interfaces/IStoragePool.sol";
import "../contracts/libraries/BLS.sol";
import "../contracts/libraries/Merkle.sol";

abstract contract StorageProof is IStorageProof, IStoragePool, OwnableUpgradeable, UUPSUpgradeable, ReentrancyGuardUpgradeable, PausableUpgradeable, AccessControlUpgradeable {
    uint256 public constant IMPLEMENTATION_VERSION = 1;
    bytes32 public constant PROOF_MANAGER_ROLE = keccak256("PROOF_MANAGER_ROLE");
    uint256 private constant MAX_TIME_DRIFT = 1 hours;
    uint256 public constant POST_CHALLENGE_INTERVAL = 24 hours; // How often PoSt challenges occur
    uint256 public constant POREP_CHALLENGE_COUNT = 10; // Number of challenges for PoRep
    
    StorageToken public token;
    IRewardEngine public rewardEngine;
    IStoragePool public storagePool;
    
    mapping(uint32 => mapping(address => Claim[])) public claims;
    mapping(string => mapping(address => UploadRequest)) public uploads;
    mapping(string => RemovalRequest) public removals;
    mapping(address => uint256) public TotalStorage; // Total storage quota for each user
    mapping(address => uint256) public UsedStorage;  // Used storage for each user
    mapping(string => address[]) public cidUploaders; // Maps a CID to its list of uploaders
    mapping(string => mapping(address => bool)) public cidCountedForUploader; // Tracks if a CID is counted towards an uploader's UsedStorage
    mapping(string => mapping(address => Challenge)) public challenges; // Tracks challenges issued for CIDs
    mapping(uint256 => IStoragePool.Pool) public pools;
    mapping(address => bytes32) public replicationSeeds; // Unique seed for each provider's replications
    mapping(string => mapping(address => bytes32)) public sealedSectors; // Sealed sector commitments
    mapping(address => uint256) public lastSpacetimeProof; // Last time a provider submitted a PoSt

    function initialize(address _token, address initialOwner, address _rewardEngine) public reinitializer(1) {
        require(initialOwner != address(0), "Invalid owner address");
        __Ownable_init(initialOwner);
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();
        __Pausable_init();
        __AccessControl_init();
        _grantRole(DEFAULT_ADMIN_ROLE, initialOwner); // Owner has admin role
        _grantRole(PROOF_MANAGER_ROLE, initialOwner); // Assign initial roles
        token = StorageToken(_token);
        rewardEngine = IRewardEngine(_rewardEngine);
    }

    function emergencyPauseProof() external onlyOwner {
        _pause();
        emit EmergencyAction("Contract paused", block.timestamp);
    }

    function emergencyUnpauseProof() external onlyOwner {
        _unpause();
        emit EmergencyAction("Contract unpaused", block.timestamp);
    }

    modifier whenInitialized() {
        require(address(token) != address(0), "Contract not initialized");
        _;
    }

    modifier validateCIDs(string[] memory cids) {
        require(cids.length > 0, "Empty CID array");
        require(cids.length <= 300, "Too many CIDs");
        for(uint i = 0; i < cids.length; i++) {
            // Inline the validation logic to save gas instead of calling validateCID
            require(bytes(cids[i]).length > 0, "Invalid CID");
            require(bytes(cids[i]).length <= 100, "CID too long");
        }
        _;
    }

    modifier validateCID(string memory _cid) {
        require(bytes(_cid).length > 0, "Invalid CID");
        require(bytes(_cid).length <= 512, "CID too long"); // Set appropriate max length
        _;
    }

    function getUploadRequest(string memory cid, address uploader) external view returns (UploadRequest memory) {
        return uploads[cid][uploader];
    }

    function isValidTimestamp(uint256 timestamp) internal view returns (bool) {
        return timestamp <= block.timestamp + MAX_TIME_DRIFT;
    }

    // - Get storage implementation
    function reserveStorageSpace(uint256 amount) external {
        require(amount > 0, "Invalid amount");
        require(msg.sender != address(0), "Invalid sender");
        require(token.balanceOf(msg.sender) >= amount, "Insufficient balance");
        require(token.transferFrom(msg.sender, address(this), amount), "Transfer failed");
    }

    // This method allows a user to submit a request to upload files to a data pool.
    // It validates the user's storage quota, ensures no duplicate upload requests or existing uploads for the same CID,
    // and calculates or uses the provided upload size while associating CIDs with their holders.
    function submitUploadRequest(
        string[] memory cids,
        uint8 replicationFactor,
        uint32 poolId,
        uint256 reportedSize // File size in bytes, provided by the user or 0 to estimate
    ) external whenNotPaused validateCIDs(cids) {
        require(replicationFactor > 0 && replicationFactor <= 12, "Invalid replication factor");
        uint256 cidLength = cids.length;
        require(cidLength > 0, "Empty CID array");

        // Calculate total file size: use provided `reportedSize` or estimate based on number of CIDs
        uint256 totalEstimatedSize = reportedSize > 0 ? reportedSize : cidLength * 256; // Assume each CID represents 256 bytes if size is not provided

        // Validate user's storage quota (TotalStorage - UsedStorage should accommodate estimated size)
        require(UsedStorage[msg.sender] + totalEstimatedSize <= TotalStorage[msg.sender], "Insufficient storage quota");
        
        string[] memory requestCids = new string[](cidLength);
        uint256 requestedCount = cidLength;
        for (uint256 i = 0; i < cidLength; i++) {
            string memory cid = cids[i];
            
            // Ensure no duplicate upload requests or existing uploads for the same CID
            require(uploads[cid][msg.sender].timestamp == 0, "Duplicate upload request");

            // If the CID is marked for removal, remove it from the removal list
            if (removals[cid].timestamp > 0) {
                delete removals[cid];
            }

            // Create a new upload request for this CID
            UploadRequest storage request = uploads[cid][msg.sender];
            request.replicationFactor = replicationFactor;
            request.poolId = poolId;
            request.uploader = msg.sender;
            request.timestamp = block.timestamp;
            request.uploadSize = totalEstimatedSize; // Store calculated or provided file size

            // Associate this CID with the uploader (msg.sender)
            cidUploaders[cid].push(msg.sender);
            requestCids[i] = cid;
            requestedCount++;
        }
        // Resize the requestCids array to the actual number of requested CIDs
        assembly {
            mstore(requestCids, requestedCount)
        }
        emit UploadRequested(requestCids, msg.sender, poolId);
    }

    // This method allows a user to submit claims for multiple CIDs.
    // It validates uploader storage quotas, updates UsedStorage for uploaders, and handles edge cases like insufficient quota.
    function submitClaim(
        string[] memory cids,
        uint32 poolId,
        uint256[] memory actualSizes, // Actual sizes of each CID in bytes
        uint256 totalStoredSize
    ) external nonReentrant whenNotPaused validateCIDs(cids) {
        require(storagePool.isProviderActive(msg.sender), "Not an active provider");
        uint256 totalClaimedSize = 0;
        require(cids.length > 0, "No CIDs provided");
        require(cids.length == actualSizes.length, "Mismatched CIDs and sizes");

        // Ensure the claim submitter is a current member of the pool
        Pool storage pool = pools[poolId];
        require(pool.members[msg.sender].joinDate > 0, "Not a member of the pool");

        uint256 cidsLength = cids.length;

        string[] memory claimedCids = new string[](cidsLength);
        uint256 claimedCount = 0;
        for (uint256 i = 0; i < cidsLength; i++) {
            string memory cid = cids[i];

            // Check if there are any unresolved challenges for the storer
            if (challenges[cid][msg.sender].challengeTimestamp > 0) {
                require(
                    block.timestamp <= challenges[cid][msg.sender].challengeTimestamp + pool.maxChallengeResponsePeriod,
                    "Unresolved challenge exists"
                );
            }

            uint256 size = actualSizes[i];
            require(size > 0, "Invalid CID size");

            // Check if the CID is marked for removal
            if (removals[cid].timestamp > 0) {
                continue; // Skip processing this CID
            }

            // Retrieve all uploaders for this CID
            address[] storage uploaders = cidUploaders[cid];
            bool quotaSatisfied = false;

            for (uint256 j = 0; j < uploaders.length; j++) {
                address uploader = uploaders[j];

                // Check if the CID is already counted towards this uploader's UsedStorage
                if (!cidCountedForUploader[cid][uploader]) {
                    // Ensure uploader has enough available quota
                    if (UsedStorage[uploader] + size <= TotalStorage[uploader]) {
                        // Update UsedStorage for this uploader
                        UsedStorage[uploader] += size;
                        cidCountedForUploader[cid][uploader] = true;
                        quotaSatisfied = true;
                    }
                } else {
                    quotaSatisfied = true; // Already counted towards this uploader's quota
                }
            }

            // If no uploader has sufficient quota, mark the CID for removal
            if (!quotaSatisfied) {
                removals[cid] = RemovalRequest({
                    cids: new string[](1),
                    uploader: address(0), // No specific uploader responsible
                    poolId: poolId,
                    timestamp: block.timestamp
                });
                removals[cid].cids[0] = cid;
                continue;
            }
            totalClaimedSize += size;
            claimedCids[i] = cid;
            claimedCount++;
        }
        // Resize the claimedCids array to the actual number of claimed CIDs
        assembly {
            mstore(claimedCids, claimedCount)
        }
        // Store claim details for the storer (claim submitter)
        claims[poolId][msg.sender].push(Claim({
            cids: claimedCids,
            storer: msg.sender,
            poolId: poolId,
            timestamp: uint40(block.timestamp),
            dataSize: totalClaimedSize
        }));
        // Distribute rewards via RewardEngine after storing the claim
        rewardEngine.distributeRewards(claimedCids, totalStoredSize, msg.sender, poolId);
        emit ClaimSubmitted(claimedCids, msg.sender, poolId);
    }

    // Initialize a provider with a unique replication seed
    function initializeProvider(address provider) external onlyRole(PROOF_MANAGER_ROLE) {
        require(replicationSeeds[provider] == bytes32(0), "Provider already initialized");
        
        // Generate a unique seed for this provider's replications
        bytes32 seed = keccak256(abi.encodePacked(
            provider,
            block.timestamp,
            blockhash(block.number - 1),
            address(this)
        ));
        
        replicationSeeds[provider] = seed;
        lastSpacetimeProof[provider] = block.timestamp;
        
        emit ProviderInitialized(provider, seed);
    }

    // Submit a Proof of Replication for a CID
    function submitProofOfReplication(
        string memory cid,
        uint32 poolId,
        bytes32 commR, // Commitment to the replica
        bytes32[] memory inclusionProofs, // Merkle proofs for challenged sectors
        uint256[] memory challengeIndices // Indices that were challenged
    ) external nonReentrant whenNotPaused validateCID(cid) {
        require(storagePool.isProviderActive(msg.sender), "Not an active provider");
        require(replicationSeeds[msg.sender] != bytes32(0), "Provider not initialized");
        require(challengeIndices.length == POREP_CHALLENGE_COUNT, "Invalid challenge count");
        require(inclusionProofs.length == POREP_CHALLENGE_COUNT, "Invalid proof count");
        
        // Get the upload request for this CID
        UploadRequest storage request = uploads[cid][msg.sender];
        require(request.timestamp > 0, "No upload request found");
        
        // Verify the provider has a unique replica by checking the commitment
        bytes32 expectedCommR = keccak256(abi.encodePacked(cid, replicationSeeds[msg.sender]));
        require(verifyReplicaCommitment(commR, expectedCommR), "Invalid replica commitment");
        
        // Verify inclusion proofs for challenged sectors
        for (uint256 i = 0; i < POREP_CHALLENGE_COUNT; i++) {
            require(
                Merkle.verifyProof(
                    challengeIndices[i],
                    keccak256(abi.encodePacked(cid, challengeIndices[i], replicationSeeds[msg.sender])),
                    inclusionProofs[i],
                    commR
                ),
                "Invalid inclusion proof"
            );
        }
        
        // Store the sealed sector commitment
        sealedSectors[cid][msg.sender] = commR;
        
        emit ProofOfReplicationSubmitted(cid, msg.sender, commR);
    }

    // Helper function to verify replica commitment
    function verifyReplicaCommitment(bytes32 commR, bytes32 expectedCommR) internal pure returns (bool) {
        // In a real implementation, this would use zk-SNARKs or other cryptographic verification
        // For simplicity, we're just comparing the commitments
        return commR == expectedCommR;
    }

    // Issue a Proof of Spacetime challenge to a provider
    function issueSpacetimeChallenge(address provider) external onlyRole(PROOF_MANAGER_ROLE) {
        require(replicationSeeds[provider] != bytes32(0), "Provider not initialized");
        require(
            block.timestamp >= lastSpacetimeProof[provider] + POST_CHALLENGE_INTERVAL,
            "Challenge interval not elapsed"
        );
        
        // Get all CIDs claimed by this provider
        string[] memory providerCids = getProviderCids(provider);
        require(providerCids.length > 0, "No CIDs found for provider");
        
        // Generate random challenges for each CID
        uint256 randomSeed = uint256(keccak256(abi.encodePacked(
            blockhash(block.number - 1),
            provider,
            block.timestamp
        )));
        
        // For each CID, select random sectors to challenge
        for (uint256 i = 0; i < providerCids.length; i++) {
            string memory cid = providerCids[i];
            
            // Skip if no sealed sector commitment exists
            if (sealedSectors[cid][provider] == bytes32(0)) continue;
            
            // Generate challenge indices for this CID
            uint256[] memory challengeIndices = new uint256[](POREP_CHALLENGE_COUNT);
            for (uint256 j = 0; j < POREP_CHALLENGE_COUNT; j++) {
                // Generate a deterministic but unpredictable challenge index
                challengeIndices[j] = uint256(keccak256(abi.encodePacked(
                    randomSeed,
                    cid,
                    j
                ))) % 1000; // Assuming 1000 sectors per CID
            }
            
            // Store the challenge
            challenges[cid][provider] = Challenge({
                challengeTimestamp: block.timestamp,
                byteRangeStart: 0, // Not used in PoSt
                byteRangeEnd: 0,   // Not used in PoSt
                storer: provider,
                transformationKey: bytes32(0), // Not used in PoSt
                challengeIndices: challengeIndices
            });
        }
        
        emit SpacetimeChallengeIssued(provider, block.timestamp);
    }

    // Submit a Proof of Spacetime
    function submitProofOfSpacetime(
        string[] memory cids,
        bytes32[][] memory proofs
    ) external nonReentrant whenNotPaused {
        require(replicationSeeds[msg.sender] != bytes32(0), "Provider not initialized");
        require(cids.length == proofs.length, "Mismatched arrays");
        
        for (uint256 i = 0; i < cids.length; i++) {
            string memory cid = cids[i];
            Challenge storage challenge = challenges[cid][msg.sender];
            
            // Verify challenge exists and is active
            require(challenge.challengeTimestamp > 0, "No active challenge");
            require(
                block.timestamp <= challenge.challengeTimestamp + 1 days,
                "Challenge expired"
            );
            
            // Verify the proof for each challenged sector
            bytes32 sealedCommitment = sealedSectors[cid][msg.sender];
            require(sealedCommitment != bytes32(0), "No sealed commitment");
            
            for (uint256 j = 0; j < challenge.challengeIndices.length; j++) {
                uint256 index = challenge.challengeIndices[j];
                require(
                    Merkle.verifyProof(
                        index,
                        keccak256(abi.encodePacked(cid, index, replicationSeeds[msg.sender])),
                        proofs[i][j],
                        sealedCommitment
                    ),
                    "Invalid spacetime proof"
                );
            }
            
            // Clear the challenge
            delete challenges[cid][msg.sender];
        }
        
        // Update the last proof timestamp
        lastSpacetimeProof[msg.sender] = block.timestamp;
        
        // Distribute rewards for successful proof
        rewardEngine.distributeSpacetimeRewards(msg.sender, cids.length);
        
        emit ProofOfSpacetimeSubmitted(msg.sender, cids, block.timestamp);
    }

    // This method issues a challenge to verify that a storer is actually storing multiple CIDs.
    function submitChallenge(address storer, uint32 poolId, uint256 numCids) external onlyRole(PROOF_MANAGER_ROLE) {
        require(numCids > 0, "Number of CIDs must be greater than zero");

        // Generate a unique challenge seed using storer-specific data
        bytes32 challengeSeed = keccak256(abi.encodePacked(
            block.timestamp,
            storer,
            blockhash(block.number - 1),
            storagePool.getProviderUniqueIdentifier(storer),
            address(this).balance
        ));
        
        // Retrieve random claimed CIDs for the storer
        string[] memory claimedCids = getClaimedCids(storer, poolId, numCids);
        uint256 claimedCidsLength = claimedCids.length;
        require(claimedCidsLength > 0, "No claimed CIDs found for this storer");
        
        // Generate a random byte range for the challenge
        uint256 byteRangeStart = uint256(challengeSeed) % 1024;
        uint256 byteRangeEnd = byteRangeStart + 256;
        
        // Generate a unique transformation key for each storer
        bytes32 transformationKey = keccak256(abi.encodePacked(challengeSeed, storer));

        // Issue challenges for the selected CIDs
        for (uint256 i = 0; i < claimedCidsLength; i++) {
            string memory cid = claimedCids[i];

            // Generate challenge indices for this CID (for PoSt compatibility)
            uint256[] memory challengeIndices = new uint256[](POREP_CHALLENGE_COUNT);
            for (uint256 j = 0; j < POREP_CHALLENGE_COUNT; j++) {
                challengeIndices[j] = uint256(keccak256(abi.encodePacked(
                    challengeSeed,
                    cid,
                    j
                ))) % 1000;
            }

            challenges[cid][storer] = Challenge({
                challengeTimestamp: block.timestamp,
                byteRangeStart: byteRangeStart,
                byteRangeEnd: byteRangeEnd,
                storer: storer,
                transformationKey: transformationKey,
                challengeIndices: challengeIndices
            });
        }
        emit ChallengeIssued(storer, claimedCids, byteRangeStart, byteRangeEnd);
    }

    // Helper function to get random claimed CIDs for a storer
    function getClaimedCids(address storer, uint32 poolId, uint256 numCids) internal view returns (string[] memory) {
        Claim[] storage storerClaims = claims[poolId][storer];
        uint256 claimedCidsLength = storerClaims.length;
        require(claimedCidsLength > 0, "No claimed CIDs found for this storer");

        // Ensure numCids does not exceed the total number of CIDs claimed by the storer
        uint256 totalCids = 0;
        for (uint256 i = 0; i < claimedCidsLength; i++) {
            totalCids += storerClaims[i].cids.length;
        }
        require(totalCids > 0, "No CIDs found for this storer");
        if (numCids > totalCids) {
            numCids = totalCids;
        }

        // Randomly select CIDs
        string[] memory selectedCids = new string[](numCids);
        uint256 selectedCount = 0;
        while (selectedCount < numCids) {
            uint256 randomIndex = uint256(keccak256(abi.encodePacked(block.timestamp, storer, selectedCount))) % claimedCidsLength;
            string[] storage claimCids = storerClaims[randomIndex].cids;

            for (uint256 j = 0; j < claimCids.length && selectedCount < numCids; j++) {
                selectedCids[selectedCount] = claimCids[j];
                selectedCount++;
            }
        }
        
        return selectedCids;
    }

    // Helper function to get all CIDs claimed by a provider
    function getProviderCids(address provider) internal view returns (string[] memory) {
        // Count total CIDs across all pools
        uint256 totalCids = 0;
        for (uint256 poolId = 0; poolId < 100; poolId++) { // Assuming max 100 pools
            Claim[] storage providerClaims = claims[uint32(poolId)][provider];
            for (uint256 i = 0; i < providerClaims.length; i++) {
                totalCids += providerClaims[i].cids.length;
            }
        }
        
        if (totalCids == 0) return new string[](0);
        
        // Collect all CIDs
        string[] memory allCids = new string[](totalCids);
        uint256 cidIndex = 0;
        
        for (uint256 poolId = 0; poolId < 100; poolId++) {
            Claim[] storage providerClaims = claims[uint32(poolId)][provider];
            for (uint256 i = 0; i < providerClaims.length; i++) {
                string[] storage claimCids = providerClaims[i].cids;
                for (uint256 j = 0; j < claimCids.length; j++) {
                    allCids[cidIndex] = claimCids[j];
                    cidIndex++;
                }
            }
        }
        
        return allCids;
    }

    // This method allows a storer to submit proof in response to a challenge.
    function submitProof(
        string memory cid,
        uint32 poolId,
        bytes memory dataChunk,
        bytes32 proofHash
    ) external nonReentrant whenNotPaused validateCID(cid) {
        // Ensure the claim submitter is a current member of the pool
        Pool storage pool = pools[poolId];
        require(pool.members[msg.sender].joinDate > 0, "Not a member of the pool");

        Challenge storage challenge = challenges[cid][msg.sender];
        require(challenge.challengeTimestamp > 0, "No active challenge");
        require(block.timestamp <= challenge.challengeTimestamp + 7 days, "Challenge expired");

        // Verify the data chunk is the correct length
        require(dataChunk.length == challenge.byteRangeEnd - challenge.byteRangeStart, "Invalid data chunk length");

        // Apply the storer-specific transformation to the data
        bytes memory transformedData = applyTransformation(dataChunk, challenge.transformationKey);
        
        // Verify proof by comparing with expected hash derived from the transformed data
        bytes32 expectedHash = keccak256(abi.encodePacked(cid, transformedData, msg.sender));
        require(proofHash == expectedHash, "Invalid proof");

        // Update status to VERIFIED
        challenge.challengeTimestamp = 0; // Reset challenge timestamp

        emit ProofSubmitted(cid, msg.sender);
    }

    function applyTransformation(bytes memory data, bytes32 key) internal pure returns (bytes memory) {
        bytes memory result = new bytes(data.length);
        
        for (uint i = 0; i < data.length; i++) {
            // XOR each byte with corresponding byte from the key
            result[i] = data[i] ^ key[i % 32];
        }
        
        return result;
    }

    // This method removes uploads for CIDs marked for removal.
    // It ensures proper cleanup of associated data and emits an event.
    function removeUpload(
        string[] memory cids,
        uint32 poolId
    ) external validateCIDs(cids) {
        uint256 cidsLength = cids.length;
        require(cidsLength > 0, "Empty CID array");

        string[] memory removedCids = new string[](cidsLength);
        uint256 removedCount = 0;
        for (uint256 i = 0; i < cidsLength; i++) {
            string memory cid = cids[i];

            // Ensure the caller has permission to remove the upload or it is marked for removal
            require(
                uploads[cid][msg.sender].uploader == msg.sender || removals[cid].timestamp > 0,
                "Not authorized to remove"
            );

            // Remove from uploads mapping
            delete uploads[cid][msg.sender];

            // Remove from cidUploaders list
            address[] storage uploaders = cidUploaders[cid];
            for (uint256 j = 0; j < uploaders.length; j++) {
                if (uploaders[j] == msg.sender) {
                    uploaders[j] = uploaders[uploaders.length - 1];
                    uploaders.pop();
                    break;
                }
            }

            // Clear removal request if it exists
            if (removals[cid].timestamp > 0) {
                delete removals[cid];
            }

            removedCids[i] = cid;
            removedCount++;
        }
        // Resize the removedCids array to the actual number of removed CIDs
        assembly {
            mstore(removedCids, removedCount)
        }
        emit RemovalRequested(removedCids, msg.sender, poolId);
    }

    function isRemoved(string memory cid) public view returns (bool) {
        return removals[cid].timestamp > 0;
    }

    function _authorizeUpgrade(address) internal override onlyOwner {}

    uint256[50] private __gap;
}
