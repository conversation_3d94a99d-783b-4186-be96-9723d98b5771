// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

/**
 * @title Merkle
 * @dev Library for Merkle tree operations used in Proof of Replication and Proof of Spacetime
 */
library Merkle {
    /**
     * @dev Verifies a Merkle proof proving the existence of a leaf in a Merkle tree.
     * @param index Leaf index in the tree
     * @param leaf Leaf data
     * @param proof Merkle proof containing sibling hashes on the branch from the leaf to the root
     * @param root Merkle root
     * @return True if the proof is valid, false otherwise
     */
    function verifyProof(
        uint256 index,
        bytes32 leaf,
        bytes32[] memory proof,
        bytes32 root
    ) internal pure returns (bool) {
        bytes32 computedHash = leaf;
        
        for (uint256 i = 0; i < proof.length; i++) {
            bytes32 proofElement = proof[i];
            
            if (index % 2 == 0) {
                // Hash(current computed hash + current element of the proof)
                computedHash = keccak256(abi.encodePacked(computedHash, proofElement));
            } else {
                // Hash(current element of the proof + current computed hash)
                computedHash = keccak256(abi.encodePacked(proofElement, computedHash));
            }
            
            // Move up one level in the tree
            index = index / 2;
        }
        
        // Check if the computed hash matches the root of the Merkle tree
        return computedHash == root;
    }
    
    /**
     * @dev Computes the Merkle root from a set of leaves
     * @param leaves Array of leaf nodes
     * @return Computed Merkle root
     */
    function computeRoot(bytes32[] memory leaves) internal pure returns (bytes32) {
        require(leaves.length > 0, "Empty leaves");
        
        if (leaves.length == 1) {
            return leaves[0];
        }
        
        uint256 n = leaves.length;
        uint256 offset = 0;
        
        // While there's more than one hash in the array, keep combining adjacent ones
        while (n > 1) {
            for (uint256 i = 0; i < n/2; i++) {
                leaves[offset + i] = keccak256(abi.encodePacked(
                    leaves[offset + 2 * i],
                    leaves[offset + 2 * i + 1]
                ));
            }
            
            // If odd number of leaves, handle the last one
            if (n % 2 == 1) {
                leaves[offset + n/2] = leaves[offset + n - 1];
                n = n/2 + 1;
            } else {
                n = n/2;
            }
        }
        
        return leaves[offset];
    }
}